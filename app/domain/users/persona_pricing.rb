module Users
  class PersonaPricing
    TEMPLATES = {
      'broker' => 'broker',
      'carrier' => 'carrier',
      'dispatcher' => 'dispatcher',
      'seller' => 'dispatcher',
      'shipper' => 'broker'
    }.freeze

    attr_reader :user

    def initialize(user)
      @user = user
    end

    def template
      if user.present?
        TEMPLATES.fetch(user.persona.type, 'carrier')
      else
        'carrier'
      end
    end

    def options
      if user.present?
        Array.wrap(template.to_sym)
      else
        %i(carrier brokerage broker dispatcher)
      end
    end
  end
end
